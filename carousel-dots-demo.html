<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图渐变指示点演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .demo-container {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }

        .carousel-container {
            position: relative;
            width: 100%;
            height: 300px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 12px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .custom-dots {
            display: flex;
            justify-content: center;
            align-items: center;
            list-style: none;
            padding: 0;
            margin: 20px 0;
            gap: 8px;
        }

        .custom-dots li {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            transform-origin: center;
        }

        .custom-dots li.active {
            background: #667eea;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        button {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        button:hover {
            background: #5a67d8;
        }

        .title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
            font-size: 28px;
            font-weight: bold;
        }

        .description {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="title">轮播图渐变指示点演示</h1>
        <p class="description">
            这个演示展示了轮播图指示点的渐变大小效果。当前活跃的指示点最大，相邻的指示点中等大小，距离越远的指示点越小，创造出平滑的视觉层次。
        </p>
        
        <div class="carousel-container">
            <span id="slide-text">幻灯片 1</span>
        </div>
        
        <ul class="custom-dots" id="dots-container">
            <!-- 指示点将通过JavaScript动态生成 -->
        </ul>
        
        <div class="controls">
            <button onclick="prevSlide()">← 上一张</button>
            <button onclick="nextSlide()">下一张 →</button>
            <button onclick="autoPlay()">自动播放</button>
            <button onclick="stopAutoPlay()">停止播放</button>
        </div>
    </div>

    <script>
        let currentIndex = 0;
        const totalSlides = 5;
        let autoPlayInterval = null;

        // 计算轮播图指示点的渐变大小样式
        function getCarouselDotStyle(dotIndex, currentIndex, total) {
            const distance = Math.abs(dotIndex - currentIndex);
            let scale = 1;
            let opacity = 1;
            
            // 根据距离计算缩放比例，实现渐变效果
            if (distance === 0) {
                scale = 1.6; // 当前活跃点最大
                opacity = 1;
            } else if (distance === 1) {
                scale = 1.2; // 相邻点中等大小
                opacity = 0.8;
            } else if (distance === 2) {
                scale = 1.0; // 距离2的点正常大小
                opacity = 0.6;
            } else {
                scale = 0.8; // 更远的点最小
                opacity = 0.4;
            }
            
            return {
                transform: `scale(${scale})`,
                opacity: opacity
            };
        }

        // 更新指示点样式
        function updateDots() {
            const dots = document.querySelectorAll('.custom-dots li');
            dots.forEach((dot, index) => {
                const style = getCarouselDotStyle(index, currentIndex, totalSlides);
                dot.style.transform = style.transform;
                dot.style.opacity = style.opacity;
                
                // 更新活跃状态
                if (index === currentIndex) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
            
            // 更新幻灯片文本
            document.getElementById('slide-text').textContent = `幻灯片 ${currentIndex + 1}`;
        }

        // 初始化指示点
        function initDots() {
            const container = document.getElementById('dots-container');
            container.innerHTML = '';
            
            for (let i = 0; i < totalSlides; i++) {
                const dot = document.createElement('li');
                dot.addEventListener('click', () => {
                    currentIndex = i;
                    updateDots();
                });
                container.appendChild(dot);
            }
            
            updateDots();
        }

        // 上一张
        function prevSlide() {
            currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
            updateDots();
        }

        // 下一张
        function nextSlide() {
            currentIndex = (currentIndex + 1) % totalSlides;
            updateDots();
        }

        // 自动播放
        function autoPlay() {
            stopAutoPlay();
            autoPlayInterval = setInterval(nextSlide, 2000);
        }

        // 停止自动播放
        function stopAutoPlay() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initDots();
            autoPlay(); // 自动开始播放
        });
    </script>
</body>
</html>
