<template>
  <div
    v-if="cateInfo?.goodsList?.length"
    :data-spm-box="cateInfo?.spmCode"
    :data-spm-param="cateInfo?.tagId || cateInfo?.selectorId || null"
    class="bg-white pb-[0.32rem]"
  >
    <a :href="linkUrl">
      <img
        loading="lazy"
        class="mb-[0.08rem]"
        v-if="cateInfo?.bannerUrl"
        :src="cateInfo?.bannerUrl"
        :alt="
          cateInfo?.tagName ||
          cateInfo?.categoryName ||
          cateInfo?.selectorName ||
          cateInfo?.title
        "
        referrerpolicy="no-referrer"
      />
    </a>
    <div v-if="!cateInfo?.bannerUrl" class="pt-[0.32rem]">
      <a
        class="inline-block relative w-full mb-[0.28rem] px-[0.4rem]"
        :href="linkUrl"
      >
        <div
          class="w-[5.2rem] mx-auto text-center line-clamp-2 text-[0.4rem] leading-[0.44rem]"
        >
          {{
            cateInfo?.tagName ||
            cateInfo?.categoryName ||
            cateInfo?.selectorName ||
            cateInfo?.title
          }}
        </div>
        <img
          loading="lazy"
          alt="arrow"
          class="w-[0.2rem] absolute right-[0.4rem] top-[50%] translate-y-[-50%]"
          src="@/assets/icons/common/arrow-right-regular-black.svg"
        />
      </a>
    </div>
    <div
      class="hidden-scrollbar flex gap-[0.08rem] overflow-x-auto pl-[0.16rem]"
    >
      <div
        class="w-[2.8rem] flex-shrink-0"
        v-for="goods in cateInfo?.goodsList"
        :key="goods?.goodsId"
      >
        <a
          v-bind:href="`/h5/goods/${goods.goodsId}${
            goods.padc ? `?padc=${goods.padc}` : ''
          }`"
          class="w-full flex flex-col gap-[0.12rem]"
        >
          <img
            loading="lazy"
            class="w-[2.8rem] h-[2.8rem] rounded-[0.08rem]"
            :alt="goods.goodsName"
            :src="goods.mainImageUrl"
            referrerpolicy="no-referrer"
          />
          <p
            class="text-[0.28rem] leading-[0.28rem] text-[#7F7F7F] line-clamp-2"
          >
            {{ goods.goodsName }}
          </p>
          <p
            class="text-[0.32rem] leading-[0.32rem] font-medium flex items-center gap-[0.04rem]"
          >
            <span class="text-[0.28rem] leading-[0.28rem]">{{
              monetaryUnit
            }}</span>
            <span>{{ setNewUnit(goods.minPrice, true) }}</span>
          </p>
          <div
            v-if="goods.pcsEstimateFreight"
            class="text-left bg-[#FAFAFA] rounded-tl-[0.16rem] rounded-br-[0.16rem] rounded-bl-[0.16rem] w-[fit-content] flex flex-col"
          >
            <div
              class="px-[0.12rem] inline-block text-white bg-[#e50113] rounded-tl-[0.16rem] rounded-tr-[0.02rem] rounded-br-[0.16rem] rounded-bl-[0.02rem] h-[0.32rem] text-[0.24rem] leading-[0.32rem] tracking-[-0.01rem] whitespace-nowrap"
            >
              {{ authStore.i18n("cm_goods.shippingCost") }}
            </div>
            <div
              class="text-[0.24rem] leading-[0.24rem] p-[0.08rem] text-[#333]"
            >
              {{ setUnit(goods.pcsEstimateFreight) }} /{{
                goods?.goodsPriceUnitName
              }}
            </div>
          </div>
        </a>
      </div>
      <a
        :href="linkUrl"
        class="w-[86px] h-[86px] rounded-full flex justify-center items-center mt-[27px] mx-[26px] bg-[#e50113] flex-shrink-0"
      >
        <div class="text-[0.32rem] leading-[0.32rem] text-[#fff] underline">
          {{ authStore.i18n("cm_app.viewMore") }}
        </div>
      </a>
    </div>
  </div>
</template>

<script setup lang="ts" name="GoodsCard">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const props = defineProps({
  cateInfo: {
    type: Object,
    default: () => {},
  },
  goodsList: {
    type: Array,
    default: () => [],
  },
  cateColor: {
    type: Object,
    default: () => {
      return {
        textColor: "#272727",
        cateColor: "#f6f6f6",
      };
    },
  },
  tag: {
    type: String,
    default: "",
  },
});
const linkUrl = computed(() => {
  const cateInfo = props.cateInfo;
  if (!cateInfo) return "/h5/search/list";

  const params = new URLSearchParams();

  if (cateInfo.activityId) {
    if (cateInfo.selectorType === "GOODS_SELECTOR_TYPE_MARKETING_RULE") {
      params.append("marketingRuleId", cateInfo.selectorId);
    } else {
      params.append("tagId", cateInfo.selectorId);
    }
    params.append("activityId", cateInfo.activityId);
  } else if (cateInfo.categoryId) {
    params.append("categoryId", cateInfo.categoryId);
    props.cateInfo?.categoryName &&
      params.append("cateName", props.cateInfo?.categoryName);
  } else if (cateInfo.tagId) {
    params.append("tagId", cateInfo.tagId);
  } else if (cateInfo.padc) {
    params.append("padc", cateInfo.padc);
  }

  const queryString = params.toString();
  return `/h5/search/list${queryString ? `?${queryString}` : ""}`;
});
</script>

<style scoped lang="scss">
.hidden-scrollbar {
  scroll-behavior: smooth;
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>
