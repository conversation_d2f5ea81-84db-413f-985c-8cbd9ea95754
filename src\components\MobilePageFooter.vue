<template>
  <div
    class="w-full px-[0.4rem] py-[1.2rem] bg-[#F2F2F2] flex flex-col gap-[0.84rem]"
    id="footer-wrapper"
  >
    <div class="flex flex-col gap-[0.56rem]">
      <div
        v-for="(section, index) in footerSections"
        :key="index"
        class="flex flex-col gap-[0.2rem]"
      >
        <p class="text-[0.32rem] leading-[0.32rem] font-medium mb-[0.16rem]">
          {{ authStore.i18n(section.titleKey) }}
        </p>
        <a
          v-for="(link, linkIndex) in section.links"
          :key="linkIndex"
          class="text-[0.28rem] leading-[0.28rem] hover:font-medium"
          target="_blank"
          :href="link.href"
        >
          {{ authStore.i18n(link.textKey) }}
        </a>
      </div>
    </div>
    <div class="flex flex-wrap gap-x-[0.04rem] gap-y-[0.12rem] mr-[-0.24rem]">
      <img
        loading="lazy"
        class="h-[0.6rem] rounded-[0.08rem] overflow-hidden"
        v-for="(url, index) in payIcons"
        :key="index"
        :src="url"
        referrerpolicy="no-referrer"
      />
    </div>

    <div class="text-[0.36rem] leading-[0.36rem]">
      <span class="mr-[0.12rem]">Email:</span>
      <span><EMAIL></span>
    </div>
    <div>
      <a
        v-if="!userInfo?.username"
        href="/h5/user/register?pageSource=/h5"
        data-spm-box="navigation-bottom-register"
      >
        <n-button color="#db2221" class="w-[4.4rem] h-[0.8rem] rounded-[10rem]">
          <span class="text-[0.36rem] uppercase">
            {{ authStore.i18n("cm_common_registerNow") }}
          </span>
        </n-button>
      </a>
      <a
        v-if="userInfo?.username"
        href="/h5/user"
        data-spm-box="navigation-bottom-myhome"
      >
        <n-button color="#db2221" class="w-[4.4rem] h-[0.8rem] rounded-[10rem]">
          <span class="text-[0.36rem] uppercase">{{
            authStore.i18n("cm_common_myAccount")
          }}</span>
        </n-button>
      </a>
    </div>

    <div class="flex gap-[0.18rem]">
      <a
        v-for="(icon, index) in socialIcons"
        :key="index"
        :href="icon.href"
        target="_blank"
        rel="noopener noreferrer"
      >
        <img
          :alt="icon.alt"
          loading="lazy"
          :class="`${icon.alt}-icon w-[0.8rem] h-[0.8rem] cursor-pointer`"
          :src="icon.src"
        />
      </a>
    </div>
    <div
      class="text-[0.28rem] leading-[0.28rem] text-[#7F7F7F] pt-[0.28rem] border-t-1 border-[#D9D9D9]"
    >
      2024 Chilat shop Todos los derechos reservados.
    </div>
  </div>
</template>

<script setup lang="ts">
import facebookIcon from "@/assets/icons/facebook.png";
import inIcon from "@/assets/icons/in.png";
import insIcon from "@/assets/icons/ins.png";
import youtubeIcon from "@/assets/icons/youtube.png";
import tiktokIcon from "@/assets/icons/tiktok.png";
import videos from "@/assets/icons/videos.svg";
import twitter from "@/assets/icons/home/<USER>";
import tiktok from "@/assets/icons/home/<USER>";
import youtube from "@/assets/icons/home/<USER>";
import facebook from "@/assets/icons/home/<USER>";
import linkedin from "@/assets/icons/home/<USER>";
import instagram from "@/assets/icons/home/<USER>";
import { useAuthStore } from "@/stores/authStore";

const userInfo = computed(() => useAuthStore().getUserInfo);

const authStore = useAuthStore();
const icons = [
  {
    href: "https://www.facebook.com/profile.php?id=61563281785563&mibextid=LQQJ4d",
    src: facebookIcon,
  },
  {
    href: "http://www.linkedin.com/in/chilat-shop-22904831a",
    src: inIcon,
  },
  {
    href: "https://www.instagram.com/chilatshop_oficial?igsh=MXB4ZWZlOG8wdGYyaQ==",
    src: insIcon,
  },
  {
    href: "https://www.youtube.com/@ChilatShop",
    src: youtubeIcon,
  },
  {
    href: "https://www.tiktok.com/@chilatshop?_t=8oJF69Z5Xlp&_r=1",
    src: tiktokIcon,
  },
  {
    href: "https://www.youtube.com/@Chilatshop-tutorial",
    src: videos,
  },
];

// 页脚链接配置
const footerSections = [
  {
    titleKey: "cm_column.aboutUs",
    links: [
      {
        textKey: "cm_news.aboutUs",
        href: "/article/about-us",
      },
      {
        textKey: "cm_news.askedQuestions",
        href: "/article/frequently-questions",
      },
      {
        textKey: "cm_news.blog",
        href: "/blog",
      },
      {
        textKey: "cm_news.invitedReward",
        href: "/article/invite",
      },
      {
        textKey: "cm_news.yiwu3DMarketMap",
        href: "/tiendas-panoramicas-en-3d",
      },
    ],
  },
  {
    titleKey: "cm_column.companyPolicy",
    links: [
      {
        textKey: "cm_news.commission",
        href: "/article/commission",
      },
      {
        textKey: "cm_news.warrantyService",
        href: "/article?code=10002",
      },
      {
        textKey: "cm_news.privacyPolicy",
        href: "/article?code=10001",
      },
      {
        textKey: "cm_news.termsOfService",
        href: "/article?code=10004",
      },
    ],
  },
  {
    titleKey: "cm_column.help",
    links: [
      {
        textKey: "cm_news.helpCenter",
        href: "/article/help-center",
      },
      {
        textKey: "cm_news.quickGuide",
        href: "/article/quick-guide",
      },
      {
        textKey: "cm_news.paymentMethods",
        href: "/article/payment-methods",
      },
      {
        textKey: "cm_news.chilatshopTutorials",
        href: "/article/tutorials",
      },
    ],
  },
];

// 社交媒体图标配置
const socialIcons = [
  {
    alt: "twitter",
    src: twitter,
    href: "https://twitter.com/chilatshop",
  },
  {
    alt: "tiktok",
    src: tiktok,
    href: "https://www.tiktok.com/@chilatshop?_t=8oJF69Z5Xlp&_r=1",
  },
  {
    alt: "youtube",
    src: youtube,
    href: "https://www.youtube.com/@ChilatShop",
  },
  {
    alt: "facebook",
    src: facebook,
    href: "https://www.facebook.com/profile.php?id=61563281785563&mibextid=LQQJ4d",
  },
  {
    alt: "linkedin",
    src: linkedin,
    href: "http://www.linkedin.com/in/chilat-shop-22904831a",
  },
  {
    alt: "instagram",
    src: instagram,
    href: "https://www.instagram.com/chilatshop_oficial?igsh=MXB4ZWZlOG8wdGYyaQ==",
  },
];

const payIcons = [
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/c3f4726c-72bb-4b6d-bf58-aa0e0019c85a.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/eccab92a-e50c-4fcb-8fe6-71c6272dff11.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/10df6bc2-6f1e-4d62-8f77-4e1938f0cb3f.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/82efd70a-9999-4a39-b379-0056244d56ed.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/fec58d91-daf4-443f-bbab-42986696609f.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/1a429d67-f273-4956-a9e4-717b840885a1.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/4c73c6b3-b79e-468b-a295-25c9256766e2.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/ca007192-e294-487a-825f-db9ce89141c5.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/d469d4cf-9e91-43e3-9d0c-8dcb0033b421.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/baadd159-c93b-478d-9a81-dbc8618553d7.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/762bf491-b80b-4eae-af50-4235305d12df.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/a54baa22-2353-45cc-9d20-3f97f1cffd57.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/1eb409a6-43bf-4c02-ac2e-0921b3018c41.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/89fe5271-3ae0-4acb-9c75-88228b6fd176.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/4214386b-966b-4f73-9ad4-7ad3eeea5daf.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/4858c059-c506-4744-83e4-c86aa43de80b.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/f56d8c3f-becd-43c4-bc28-0b7b26631f62.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/79b18fa6-5a96-419c-85ec-3f72f3e4f71d.png",
  // "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/71271e5d-13a3-41c8-9075-23555141c480.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/675092e0-09bc-42cf-93ea-bb05a4e85c24.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/b034c8fd-c3e9-4d74-a28b-dbdb5039a417.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/720e863a-6214-4f98-8400-0d0acbc7696f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/2f6d0065-df77-4a80-9449-34aff89aa510.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/492c9f46-633f-49c8-ad94-0f59eab7a70a.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/27e02613-6f5a-4fc6-b943-ef26aee73d21.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/339c5449-187c-4c6d-80fb-572586b0788a.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/ff407292-0c86-4041-8f88-b4e07b599a57.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/04359c23-2a43-421f-ba7f-ab23ff95317b.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/10f3331b-e4e0-471e-b70e-a99cd3c9150d.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/f2fe089b-d42f-4440-a37e-e35bbc3a841f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/18c90321-cd4a-4b2d-aa6e-c424539f607f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/30253f19-da84-4235-9e6b-63adb9b4d7dc.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/854d6499-06c8-446d-b422-0a154cf0b221.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/a2b16318-75d8-4c4d-afbe-617ac988ed66.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/8e981f15-afb1-4efd-99a6-814347dae5a1.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/0523fdd5-4678-4a6a-9f77-20b85d1ac2b7.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/b9b1656b-86ef-4062-9805-2347f5ce8476.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/08/25/470702ba-382f-4183-a79b-f5c827ecc3fa.jpg",
];
</script>
<style scoped lang="scss">
.twitter-icon:hover {
  content: url("@/assets/icons/home/<USER>");
}
.tiktok-icon:hover {
  content: url("@/assets/icons/home/<USER>");
}
.youtube-icon:hover {
  content: url("@/assets/icons/home/<USER>");
}
.facebook-icon:hover {
  content: url("@/assets/icons/home/<USER>");
}
.linkedin-icon:hover {
  content: url("@/assets/icons/home/<USER>");
}
.instagram-icon:hover {
  content: url("@/assets/icons/home/<USER>");
}
:deep(.n-button) {
  --n-color-hover: #f20114 !important;
  --n-color-focus: #f20114 !important;
  --n-wave-opacity: 1 !important;
}
</style>
